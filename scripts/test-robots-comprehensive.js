#!/usr/bin/env node

// Comprehensive robots.txt testing suite
// File: /scripts/test-robots-comprehensive.js

const fs = require('fs');
const path = require('path');

// Import our robots.txt logic for testing
let generateRobotsContent, createSlug;

try {
  // Try to import the actual API module
  const robotsModule = require('../api/robots.js');
  // Since it's an ES module, we'll need to test it differently
} catch (error) {
  console.log('Using local test implementation for comprehensive testing...');
}

// Test configuration with various scenarios
const TEST_SCENARIOS = {
  production: {
    environment: 'production',
    expectedFeatures: [
      'Allow: /',
      'Disallow: /api/',
      'Disallow: /.env',
      'Sitemap:',
      'User-agent: GPTBot',
      'Disallow: /.git/',
      'Allow: /blog/',
      'Crawl-delay: 1',
      'Request-rate: 1/10s'
    ],
    forbiddenFeatures: [
      'Disallow: /',
      'Crawl-delay: 86400'
    ]
  },
  development: {
    environment: 'development',
    expectedFeatures: [
      'Disallow: /',
      'Crawl-delay: 86400',
      'Development environment',
      'Sitemap:'
    ],
    forbiddenFeatures: [
      'Allow: /',
      'Allow: /blog/',
      'Blog posts - explicitly allowed'
    ]
  }
};

// Mock different configurations for testing
const TEST_CONFIGS = {
  minimal: {
    baseUrl: 'https://test.com',
    robots: {
      production: {
        allowAll: true,
        crawlDelay: 1,
        disallowPaths: ['/admin/'],
        allowPaths: ['/blog/']
      },
      development: {
        allowAll: false,
        crawlDelay: 10,
        disallowPaths: ['/'],
        allowPaths: []
      },
      botConfigs: {
        'Googlebot': {
          allow: ['/'],
          disallow: ['/admin/']
        }
      }
    }
  },
  comprehensive: {
    baseUrl: 'https://akeyreu.com',
    robots: {
      production: {
        allowAll: true,
        crawlDelay: 1,
        disallowPaths: [
          '/admin/', '/.env', '/api/', '/*?*', '/*#*',
          '/*.json$', '/*.xml$', '/*.txt$'
        ],
        allowPaths: ['/blog/', '/mindful-breaks/', '/contact/']
      },
      development: {
        allowAll: false,
        crawlDelay: 10,
        disallowPaths: ['/'],
        allowPaths: []
      },
      botConfigs: {
        'Googlebot': { allow: ['/'], disallow: ['/api/', '/admin/'], crawlDelay: 1 },
        'Bingbot': { allow: ['/'], disallow: ['/api/', '/admin/'], crawlDelay: 2 },
        'GPTBot': { allow: [], disallow: ['/'] },
        'facebookexternalhit': { allow: ['/'], disallow: [] }
      }
    }
  }
};

// Test data sets
const TEST_POSTS = [
  { title: 'Understanding Mental Health in the Digital Age', date: 'December 15, 2024' },
  { title: 'The Science of Mindfulness & Neural Plasticity!', date: 'December 10, 2024' },
  { title: 'Building Resilience Through Technology', date: 'December 5, 2024' },
  { title: 'Advanced AI Ethics: A Deep Dive', date: 'November 30, 2024' },
  { title: 'Meditation Apps: Do They Really Work?', date: 'November 25, 2024' }
];

// Helper functions (copied from our implementation)
function createSlugTest(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

async function generateRobotsContentTest(environment = 'production', config = TEST_CONFIGS.comprehensive, posts = TEST_POSTS) {
  const robotsConfig = config.robots[environment] || config.robots.production;
  const botConfigs = config.robots.botConfigs;
  const baseUrl = config.baseUrl;
  
  let content = '';
  
  // Main user-agent rules
  content += 'User-agent: *\n';
  
  if (robotsConfig.allowAll) {
    content += 'Allow: /\n';
  } else {
    content += 'Disallow: /\n';
  }
  
  content += '\n';
  
  // Add specific disallow rules (avoid duplicates)
  if (robotsConfig.disallowPaths && robotsConfig.disallowPaths.length > 0) {
    const uniquePaths = [...new Set(robotsConfig.disallowPaths)];
    // Don't duplicate the main "Disallow: /" if already set above
    const pathsToAdd = robotsConfig.allowAll ? uniquePaths : uniquePaths.filter(path => path !== '/');

    if (pathsToAdd.length > 0) {
      pathsToAdd.forEach(path => {
        content += `Disallow: ${path}\n`;
      });
      content += '\n';
    }
  }
  
  // Add specific allow rules
  if (robotsConfig.allowPaths && robotsConfig.allowPaths.length > 0) {
    robotsConfig.allowPaths.forEach(path => {
      content += `Allow: ${path}\n`;
    });
    content += '\n';
  }
  
  // Add crawl delay
  if (robotsConfig.crawlDelay) {
    content += `Crawl-delay: ${robotsConfig.crawlDelay}\n\n`;
  }
  
  // Add bot-specific configurations
  Object.entries(botConfigs).forEach(([botName, botConfig]) => {
    content += `User-agent: ${botName}\n`;
    
    if (botConfig.allow && botConfig.allow.length > 0) {
      botConfig.allow.forEach(path => {
        content += `Allow: ${path}\n`;
      });
    }
    
    if (botConfig.disallow && botConfig.disallow.length > 0) {
      botConfig.disallow.forEach(path => {
        content += `Disallow: ${path}\n`;
      });
    }
    
    if (botConfig.crawlDelay) {
      content += `Crawl-delay: ${botConfig.crawlDelay}\n`;
    }
    
    content += '\n';
  });
  
  // Add dynamic blog post allows for better SEO
  if (environment === 'production' && posts && posts.length > 0) {
    content += `# Blog posts - explicitly allowed for SEO\n`;
    posts.slice(0, 10).forEach(post => {
      const slug = createSlugTest(post.title);
      content += `Allow: /blog/${slug}\n`;
    });
    content += '\n';
  }
  
  // Add sitemap reference
  content += `# Sitemap\n`;
  content += `Sitemap: ${baseUrl}/sitemap.xml\n\n`;
  
  // Add environment-specific content
  if (environment === 'production') {
    content += `# Generated dynamically on ${new Date().toISOString()}\n`;
    content += `# For questions about this robots.txt, contact: <EMAIL>\n`;
    content += `# Website: ${baseUrl}\n\n`;

    // Add security-focused rules
    content += `# Security and privacy protection\n`;
    content += `Disallow: /.git/\n`;
    content += `Disallow: /.env*\n`;
    content += `Disallow: /node_modules/\n`;
    content += `Disallow: /package*.json\n`;
    content += `Disallow: /vercel.json\n`;
    content += `Disallow: /.vercel/\n`;
    content += `Disallow: /api/test*\n`;
    content += `Disallow: /api/debug*\n\n`;

    // Add performance-focused rules
    content += `# Performance optimization - block resource files\n`;
    content += `Disallow: /build/\n`;
    content += `Disallow: /*.map$\n`;
    content += `Disallow: /static/js/*.chunk.js\n`;
    content += `Disallow: /static/css/*.chunk.css\n`;
    content += `Disallow: /static/media/\n\n`;

    // Add SEO-focused rules
    content += `# SEO optimization\n`;
    content += `Disallow: /*?utm_*\n`;
    content += `Disallow: /*?ref=*\n`;
    content += `Disallow: /*?source=*\n`;
    content += `Disallow: /*&utm_*\n`;
    content += `Disallow: /*&ref=*\n`;
    content += `Disallow: /*&source=*\n`;
    content += `Disallow: /search?*\n`;
    content += `Disallow: /*?search=*\n\n`;

    // Add mobile-specific optimizations
    content += `# Mobile optimization\n`;
    content += `Allow: /static/css/\n`;
    content += `Allow: /static/js/main*.js\n`;
    content += `Allow: /favicon.ico\n`;
    content += `Allow: /manifest.json\n\n`;

    // Add comprehensive security rules
    content += `# Additional security rules\n`;
    content += `Disallow: /node_modules/\n`;
    content += `Disallow: /package*.json\n`;
    content += `Disallow: /vercel.json\n\n`;

    // Add specific bot instructions for better SEO
    content += `# Additional bot-specific rules\n`;
    content += `User-agent: GPTBot\n`;
    content += `Disallow: /\n\n`;

    content += `User-agent: ChatGPT-User\n`;
    content += `Disallow: /\n\n`;

    content += `User-agent: CCBot\n`;
    content += `Disallow: /\n\n`;

    content += `User-agent: anthropic-ai\n`;
    content += `Disallow: /\n\n`;

    content += `User-agent: Claude-Web\n`;
    content += `Disallow: /\n\n`;

    // Add crawl budget optimization
    content += `# Crawl budget optimization\n`;
    content += `User-agent: *\n`;
    content += `Request-rate: 1/10s\n\n`;

  } else {
    content += `# Development environment - crawling restricted\n`;
    content += `# Generated on ${new Date().toISOString()}\n`;
    content += `# This is a development/staging environment\n\n`;

    // Add development-specific restrictions
    content += `# Development restrictions\n`;
    content += `Disallow: /\n`;
    content += `Crawl-delay: 86400\n\n`;
  }
  
  return content;
}

// Validation functions
function validateRobotsContent(content, scenario) {
  const results = [];
  const lines = content.split('\n');
  
  // Check expected features
  scenario.expectedFeatures.forEach(feature => {
    const found = content.includes(feature);
    results.push({
      type: 'expected',
      feature,
      passed: found,
      message: found ? `✅ Found: ${feature}` : `❌ Missing: ${feature}`
    });
  });
  
  // Check forbidden features
  scenario.forbiddenFeatures.forEach(feature => {
    const found = content.includes(feature);
    results.push({
      type: 'forbidden',
      feature,
      passed: !found,
      message: !found ? `✅ Correctly absent: ${feature}` : `❌ Should not contain: ${feature}`
    });
  });
  
  // Additional validations
  const userAgentCount = (content.match(/User-agent:/g) || []).length;
  results.push({
    type: 'structure',
    feature: 'User-agent directives',
    passed: userAgentCount >= 2,
    message: `${userAgentCount >= 2 ? '✅' : '❌'} Found ${userAgentCount} User-agent directives`
  });
  
  const sitemapCount = (content.match(/Sitemap:/g) || []).length;
  results.push({
    type: 'structure',
    feature: 'Sitemap directive',
    passed: sitemapCount === 1,
    message: `${sitemapCount === 1 ? '✅' : '❌'} Found ${sitemapCount} Sitemap directive(s)`
  });
  
  return results;
}

// Performance tests
function performanceTest(content) {
  const results = [];
  const lines = content.split('\n');
  const size = Buffer.byteLength(content, 'utf8');
  
  results.push({
    test: 'File size',
    passed: size < 32768, // 32KB limit recommended
    message: `${size < 32768 ? '✅' : '❌'} Size: ${size} bytes (limit: 32KB)`
  });
  
  results.push({
    test: 'Line count',
    passed: lines.length < 1000,
    message: `${lines.length < 1000 ? '✅' : '❌'} Lines: ${lines.length} (recommended < 1000)`
  });
  
  const disallowCount = (content.match(/Disallow:/g) || []).length;
  results.push({
    test: 'Disallow rules',
    passed: disallowCount < 100,
    message: `${disallowCount < 100 ? '✅' : '❌'} Disallow rules: ${disallowCount} (recommended < 100)`
  });
  
  return results;
}

// Security tests
function securityTest(content, environment) {
  const results = [];
  
  const securityPaths = [
    '/.env', '/.git/', '/admin/', '/api/', '/node_modules/',
    '/package.json', '/vercel.json'
  ];
  
  securityPaths.forEach(path => {
    const blocked = content.includes(`Disallow: ${path}`);
    if (environment === 'production') {
      results.push({
        test: `Security: ${path}`,
        passed: blocked,
        message: `${blocked ? '✅' : '❌'} ${path} ${blocked ? 'blocked' : 'not blocked'}`
      });
    }
  });
  
  // Check AI bot blocking
  const aiBots = ['GPTBot', 'ChatGPT-User', 'CCBot', 'anthropic-ai'];
  aiBots.forEach(bot => {
    const blocked = content.includes(`User-agent: ${bot}`) && content.includes('Disallow: /');
    if (environment === 'production') {
      results.push({
        test: `AI Bot: ${bot}`,
        passed: blocked,
        message: `${blocked ? '✅' : '❌'} ${bot} ${blocked ? 'blocked' : 'not blocked'}`
      });
    }
  });
  
  return results;
}

// Main testing function
async function runComprehensiveTests() {
  console.log('🧪 Comprehensive Robots.txt Testing Suite');
  console.log('==========================================\n');
  
  let totalTests = 0;
  let passedTests = 0;
  
  // Test different configurations
  for (const [configName, config] of Object.entries(TEST_CONFIGS)) {
    console.log(`📋 Testing Configuration: ${configName.toUpperCase()}`);
    console.log('='.repeat(50));
    
    for (const [scenarioName, scenario] of Object.entries(TEST_SCENARIOS)) {
      console.log(`\n🔍 Environment: ${scenarioName}`);
      console.log('-'.repeat(30));
      
      const content = await generateRobotsContentTest(scenario.environment, config, TEST_POSTS);
      
      // Validate content
      const validationResults = validateRobotsContent(content, scenario);
      const performanceResults = performanceTest(content);
      const securityResults = securityTest(content, scenario.environment);
      
      const allResults = [...validationResults, ...performanceResults, ...securityResults];
      
      allResults.forEach(result => {
        totalTests++;
        if (result.passed) passedTests++;
        console.log(result.message);
      });
      
      // Show content preview
      console.log('\n📄 Content Preview (first 10 lines):');
      console.log('-'.repeat(40));
      content.split('\n').slice(0, 10).forEach((line, i) => {
        console.log(`${String(i + 1).padStart(2)}: ${line}`);
      });
      console.log('...\n');
    }
  }
  
  // Test slug generation
  console.log('🔤 Testing Slug Generation');
  console.log('='.repeat(30));
  
  const slugTests = [
    { input: 'Understanding Mental Health in the Digital Age', expected: 'understanding-mental-health-in-the-digital-age' },
    { input: 'The Science of Mindfulness & Neural Plasticity!', expected: 'the-science-of-mindfulness-neural-plasticity' },
    { input: 'Building Resilience Through Technology', expected: 'building-resilience-through-technology' },
    { input: 'Advanced AI Ethics: A Deep Dive', expected: 'advanced-ai-ethics-a-deep-dive' },
    { input: 'Test with    Multiple   Spaces', expected: 'test-with-multiple-spaces' }
  ];
  
  slugTests.forEach(test => {
    totalTests++;
    const result = createSlugTest(test.input);
    const passed = result === test.expected;
    if (passed) passedTests++;
    
    console.log(`${passed ? '✅' : '❌'} "${test.input}" → "${result}"`);
    if (!passed) {
      console.log(`   Expected: "${test.expected}"`);
    }
  });
  
  // Final results
  console.log('\n🎯 Test Results Summary');
  console.log('='.repeat(30));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Implementation is ready for production.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
  }
  
  return { totalTests, passedTests, successRate: (passedTests / totalTests) * 100 };
}

// Export for use in other scripts
module.exports = {
  runComprehensiveTests,
  generateRobotsContentTest,
  validateRobotsContent,
  performanceTest,
  securityTest,
  createSlugTest
};

// Run tests if called directly
if (require.main === module) {
  runComprehensiveTests().catch(console.error);
}

{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}, {"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/posts/([^/]+)$", "dest": "/api/posts/[slug].js?slug=$1"}, {"src": "/api/categories$", "dest": "/api/metadata?type=categories"}, {"src": "/api/categories/([^/]+)$", "dest": "/api/metadata?category=$1"}, {"src": "/api/tags$", "dest": "/api/metadata?type=tags"}, {"src": "/api/tags/([^/]+)$", "dest": "/api/metadata?tag=$1"}, {"src": "/api/featured$", "dest": "/api/search?featured=true"}, {"src": "/api/rss$", "dest": "/api/feeds?format=rss"}, {"src": "/api/feed.json$", "dest": "/api/feeds?format=json"}, {"src": "/api/auth$", "dest": "/api/auth"}, {"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "^/static/(.*)", "dest": "/static/$1"}, {"src": "^/favicon.ico", "dest": "/favicon.ico"}, {"src": "^/manifest.json", "dest": "/manifest.json", "headers": {"Content-Type": "application/json"}}, {"src": "^/sw.js", "dest": "/sw.js", "headers": {"Content-Type": "application/javascript"}}, {"src": "^/robots.txt", "dest": "/api/robots"}, {"src": "^/sitemap.xml", "dest": "/api/sitemap"}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html"}]}
# Environment Variables Template for Akeyreu
# Copy this file to .env.local for development or configure in production

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=development

# Site URLs
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Settings (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_METHODS=GET,POST,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With,Accept,Origin
CORS_CREDENTIALS=false
CORS_MAX_AGE=300

# Security Features
ENABLE_RATE_LIMIT=true
ENABLE_SECURITY_HEADERS=true
REQUIRE_ORIGIN_VALIDATION=false
MAX_REQUEST_SIZE=1048576

# Admin Authentication (REQUIRED)
ADMIN_USERNAME=your-admin-username
ADMIN_PASSWORD=your-secure-admin-password

# Secrets (CHANGE THESE IN PRODUCTION!)
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# EmailJS Configuration (for contact forms)
EMAILJS_SERVICE_ID=your_emailjs_service_id
EMAILJS_TEMPLATE_ID=your_emailjs_template_id
EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Legacy EmailJS (for backward compatibility)
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_public_key

# Contact Email
CONTACT_EMAIL=<EMAIL>

# =============================================================================
# ANALYTICS & MONITORING
# =============================================================================

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_id
NEXT_PUBLIC_GA_TRACKING_ID=

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================

# Cache Settings (in milliseconds)
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300000
CACHE_POST_TTL=600000
CACHE_SEARCH_TTL=120000
CACHE_MAX_SIZE=100

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate Limit Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESS=false
RATE_LIMIT_SKIP_FAILED=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging Settings
LOG_LEVEL=debug
LOG_CONSOLE=true
LOG_FILE=false
LOG_REMOTE=false
LOG_REMOTE_ENDPOINT=

# =============================================================================
# SECURITY NOTES
# =============================================================================

# IMPORTANT SECURITY REMINDERS:
# 1. Never commit .env files to version control
# 2. Use strong, unique secrets for SESSION_SECRET and JWT_SECRET
# 3. Set REQUIRE_ORIGIN_VALIDATION=true in production
# 4. Use HTTPS URLs in production
# 5. Regularly rotate secrets and API keys
# 6. Monitor logs for suspicious activity
# 7. Keep dependencies updated

# =============================================================================
# DEPLOYMENT CHECKLIST
# =============================================================================

# Before deploying to production:
# □ Change all default secrets
# □ Set NODE_ENV=production
# □ Configure proper CORS_ORIGINS
# □ Set REQUIRE_ORIGIN_VALIDATION=true
# □ Use HTTPS URLs
# □ Configure proper logging
# □ Set up monitoring and alerts
# □ Test all security features
# □ Run security audit: npm run security:audit

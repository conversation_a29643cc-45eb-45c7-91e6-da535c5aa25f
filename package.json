{"name": "akeyreuwebsiteupdate", "version": "1.0.0", "main": "index.js", "scripts": {"start": "craco start", "build": "craco build", "dev": "node scripts/dev-server.js", "dev:vercel": "npx vercel dev", "test": "craco test", "eject": "react-scripts eject", "deploy": "vercel --prod", "preview": "vercel", "clean": "rm -rf build node_modules/.cache", "posts:list": "node scripts/manage-posts.js list", "posts:add": "node scripts/manage-posts.js add", "posts:remove": "node scripts/manage-posts.js remove", "posts:validate": "node scripts/manage-posts.js validate", "posts:migrate": "node scripts/migrate-posts-schema.js", "test:api": "node scripts/test-api.js", "test:robots": "node scripts/test-robots.js", "test:robots:comprehensive": "node scripts/test-robots-comprehensive.js", "test:robots:live": "node scripts/test-live-endpoint.js", "test:ux": "node scripts/test-ux-improvements.js", "security:audit": "node scripts/security-audit.js", "security:test": "node scripts/test-security-implementation.js", "security:update": "node scripts/dependency-update.js", "security:update:auto": "node scripts/dependency-update.js --auto", "security:fix": "npm audit fix", "sitemap:generate": "node scripts/generate-sitemap.js", "seo:audit": "npm run build && node scripts/seo-audit.js", "seo:live": "node scripts/live-seo-test.js", "seo:full": "npm run sitemap:generate && npm run posts:validate && npm run test:robots:comprehensive && npm run seo:audit", "ux:audit": "npm run test:ux", "full:audit": "npm run seo:audit && npm run ux:audit && npm run security:audit", "analyze": "ANALYZE=true npm run build", "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "bootstrap": "^5.3.3", "emailjs-com": "^3.2.0", "http-proxy-middleware": "^3.0.5", "react": "^18.2.0", "react-bootstrap": "^2.10.7", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^7.0.2", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "slugify": "^1.6.6", "web-vitals": "^4.2.4"}, "devDependencies": {"@craco/craco": "^7.1.0", "@fullhuman/postcss-purgecss": "^7.0.2", "@vercel/node": "^3.0.0", "autoprefixer": "^10.4.21", "babel-plugin-transform-remove-console": "^6.9.4", "craco-alias": "^3.0.1", "express": "^5.1.0", "jsdom": "^26.1.0", "postcss-cli": "^11.0.1", "puppeteer": "^24.10.2", "react-scripts": "^5.0.1", "sass": "^1.89.2", "vercel": "^43.2.0", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
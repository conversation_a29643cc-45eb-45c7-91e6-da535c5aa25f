/* BlogAnalytics.css - Styles for blog analytics dashboard */

.blog-analytics {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.analytics-header {
  text-align: center;
  margin-bottom: 3rem;
}

.analytics-header h2 {
  color: #111827;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.analytics-header p {
  color: #6b7280;
  font-size: 1.125rem;
}

/* Key Metrics */
.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 2rem;
  background: var(--stat-color, #3b82f6);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.25rem 0;
  line-height: 1;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.25rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

/* Content Distribution */
.content-distribution {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.distribution-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.distribution-section h3 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.progress-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
}

.progress-bar {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Content Health */
.content-health {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 3rem;
}

.content-health h3 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.health-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.health-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #059669;
}

/* Actions */
.analytics-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.refresh-button,
.view-blog-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button:hover,
.view-blog-button:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.view-blog-button {
  background: #059669;
}

.view-blog-button:hover {
  background: #047857;
}

.refresh-button:focus,
.view-blog-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading State */
.blog-analytics.loading {
  opacity: 0.7;
}

.analytics-skeleton {
  margin-bottom: 3rem;
}

.skeleton-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.skeleton-card {
  height: 120px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Error State */
.blog-analytics.error .error-message {
  text-align: center;
  padding: 3rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  color: #991b1b;
}

.retry-button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background: #b91c1c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-analytics {
    padding: 1rem;
  }
  
  .key-metrics {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .content-distribution {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .distribution-section {
    padding: 1.5rem;
  }
  
  .health-metrics {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .analytics-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .refresh-button,
  .view-blog-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .analytics-header h2 {
    font-size: 1.5rem;
  }
  
  .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
}

.optimized-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.optimized-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: scale(1.05);
}

.optimized-image.loaded {
  opacity: 1;
  transform: scale(1);
}

.optimized-image.error {
  opacity: 0.5;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  background-size: cover;
  background-position: center;
  filter: blur(5px);
  transition: opacity 0.3s ease;
}

.optimized-image.loaded + .image-placeholder {
  opacity: 0;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.image-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
  font-size: 14px;
  z-index: 2;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .optimized-image-container {
    border-radius: 4px;
  }
  
  .loading-spinner {
    width: 20px;
    height: 20px;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .optimized-image,
  .image-placeholder,
  .loading-spinner {
    transition: none;
    animation: none;
  }
}

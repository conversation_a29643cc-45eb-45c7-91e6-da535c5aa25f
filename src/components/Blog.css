.blog-center-container {
    max-width: 1400px;
    margin: 80px auto;
    padding: 20px;
    text-align: center;
}

/* Blog Layout - Sidebar + Main Content */
.blog-layout {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: 2rem;
    align-items: start;
    text-align: left;
    margin-top: 2rem;
}

.blog-sidebar {
    position: sticky;
    top: 100px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.blog-main-content {
    min-width: 0; /* Prevents grid overflow */
}

.blog-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #e9ecef;
}

.blog-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #035aa6;
}

.blog-description {
    font-size: 1.125rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto 2rem auto;
    line-height: 1.6;
}

/* Search Results Summary */
.search-results-summary {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    border-radius: 0 4px 4px 0;
    text-align: left;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.search-results-summary p {
    margin: 0;
    color: #495057;
    font-weight: 500;
}

.blog-post-preview-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: center;
}

.blog-post-preview {
    width: 100%;
    max-width: 800px;
    text-align: left;
    padding: 24px;
    background-color: #ffffff !important;
    border-radius: 12px;
    text-decoration: none;
    color: #000000 !important;
    box-shadow: 0 4px 12px rgba(3, 90, 166, 0.15);
    transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e9ecef;
    position: relative;
}

.blog-post-preview.featured {
    border-left: 4px solid #ffc107;
    background-color: #ffffff !important;
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.2);
}

.blog-post-preview:hover {
    background-color: #ffffff !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(3, 90, 166, 0.3);
}

.blog-post-preview.featured:hover {
    background-color: #ffffff !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
    border-left-color: #ffc107;
}

.featured-badge {
    display: inline-block;
    background: #ffc107;
    color: #212529;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post-header {
    margin-bottom: 1rem;
}

.blog-post-title {
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.3;
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.blog-date {
    font-weight: 500;
}

.post-author {
    color: #495057;
}

.read-time {
    color: #6c757d;
    font-style: italic;
}

.blog-snippet {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #495057;
}

.post-taxonomy {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-tag {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-item {
    background: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    border: 1px solid #dee2e6;
}

/* Hover effects for enhanced elements - Disabled to maintain text readability */
/*
.blog-post-preview:hover .blog-post-title,
.blog-post-preview:hover .blog-date,
.blog-post-preview:hover .post-author,
.blog-post-preview:hover .read-time,
.blog-post-preview:hover .blog-snippet {
    color: #ffffff;
}

.blog-post-preview:hover .category-tag {
    background: #ffffff;
    color: #035aa6;
}

.blog-post-preview:hover .tag-item {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
}

.blog-post-preview:hover .featured-badge {
    background: #ffffff;
    color: #ffc107;
}
*/

/* Loading and Error States */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    margin: 2rem auto;
    max-width: 600px;
}

.error-message h2 {
    margin-bottom: 1rem;
    color: #721c24;
}

.retry-button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 1rem;
    transition: background-color 0.2s ease;
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(220, 53, 69, 0.2);
}

.retry-button:hover {
    background: #c82333;
}

.retry-button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.no-posts-message {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
    font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .blog-layout {
        grid-template-columns: 280px 1fr;
        gap: 1.5rem;
    }

    .blog-sidebar {
        top: 80px;
        max-height: calc(100vh - 100px);
    }
}

@media (max-width: 768px) {
    .blog-center-container {
        margin: 40px auto;
        padding: 15px;
    }

    .blog-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .blog-sidebar {
        position: static;
        max-height: none;
        order: -1; /* Show sidebar above content on mobile */
    }

    /* Improve touch targets on mobile */
    .blog-post-link {
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(3, 90, 166, 0.1);
    }

    .blog-title {
        font-size: 2rem;
    }

    .blog-description {
        font-size: 1rem;
    }

    .blog-post-preview {
        padding: 20px;
        max-width: 100%;
    }

    .blog-post-title {
        font-size: 1.25rem;
    }

    .post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .post-taxonomy {
        gap: 0.5rem;
    }

    .search-results-summary {
        padding: 0.75rem 1rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .blog-center-container {
        margin: 20px auto;
        padding: 10px;
    }

    .blog-title {
        font-size: 1.75rem;
    }

    .blog-post-preview {
        padding: 16px;
    }

    .blog-post-title {
        font-size: 1.125rem;
    }

    .post-categories,
    .post-tags {
        gap: 0.25rem;
    }

    .category-tag,
    .tag-item {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .blog-post-preview {
        border: 2px solid #000;
    }

    .blog-post-preview:hover {
        border-color: #fff;
    }

    .category-tag,
    .tag-item,
    .featured-badge {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .blog-post-preview {
        transition: none;
    }

    .blog-post-preview:hover {
        transform: none;
    }

    .retry-button {
        transition: none;
    }
}

/* Dark mode disabled to maintain consistent white card backgrounds */
/*
@media (prefers-color-scheme: dark) {
    .blog-post-preview {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }

    .blog-post-preview:hover {
        background-color: #4a5568;
    }

    .blog-snippet {
        color: #cbd5e0;
    }

    .post-meta {
        color: #a0aec0;
    }

    .tag-item {
        background: #4a5568;
        color: #e2e8f0;
        border-color: #718096;
    }

    .search-results-summary {
        background: #2d3748;
        color: #e2e8f0;
        border-left-color: #63b3ed;
    }

    .error-message {
        background: #742a2a;
        border-color: #9b2c2c;
        color: #fed7d7;
    }

    .no-posts-message {
        color: #a0aec0;
    }
}
*/

/* Focus styles for accessibility */
.blog-post-link:focus {
    outline: 3px solid #007bff;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
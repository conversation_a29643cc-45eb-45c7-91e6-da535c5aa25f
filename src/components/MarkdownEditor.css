/* MarkdownEditor.css - Styles for the markdown editor */

.markdown-editor {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  overflow: hidden;
}

.markdown-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
  border: none;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem 1rem;
}

.editor-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
}

.tab:hover {
  background: #e5e7eb;
  color: #374151;
}

.tab.active {
  background: #3b82f6;
  color: white;
}

.tab:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.fullscreen-btn {
  background: none;
  border: 1px solid #d1d5db;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  color: #6b7280;
  transition: all 0.2s ease;
}

.fullscreen-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.fullscreen-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.toolbar-btn {
  background: none;
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.toolbar-btn:active {
  background: #d1d5db;
}

.toolbar-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.markdown-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  resize: none;
  background: white;
  color: #374151;
}

.markdown-textarea::placeholder {
  color: #9ca3af;
}

.preview-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: white;
  min-height: 200px;
}

/* Preview Styles */
.preview-h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #111827;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.preview-h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #1f2937;
}

.preview-h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #374151;
}

.preview-p {
  margin: 0.75rem 0;
  line-height: 1.7;
  color: #374151;
}

.preview-quote {
  border-left: 4px solid #3b82f6;
  padding: 0.5rem 0 0.5rem 1rem;
  margin: 1rem 0;
  background: #f8fafc;
  font-style: italic;
  color: #64748b;
}

.preview-code-inline {
  background: #f1f5f9;
  color: #e11d48;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.preview-pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
}

.preview-code-block {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.preview-ul,
.preview-ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.preview-li {
  margin: 0.25rem 0;
  line-height: 1.6;
  color: #374151;
}

.preview-link {
  color: #3b82f6;
  text-decoration: underline;
  text-decoration-color: transparent;
  transition: text-decoration-color 0.2s ease;
}

.preview-link:hover {
  text-decoration-color: #3b82f6;
}

.preview-img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  font-size: 0.75rem;
  color: #6b7280;
}

.editor-help details {
  position: relative;
}

.editor-help summary {
  cursor: pointer;
  color: #3b82f6;
  font-weight: 500;
  list-style: none;
  padding: 0.25rem 0;
}

.editor-help summary::-webkit-details-marker {
  display: none;
}

.editor-help summary::before {
  content: '?';
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.help-content {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
  max-width: 400px;
}

.help-section {
  margin-bottom: 1rem;
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.help-section ul {
  margin: 0;
  padding-left: 1rem;
  list-style: none;
}

.help-section li {
  margin: 0.25rem 0;
  font-size: 0.75rem;
  color: #6b7280;
}

.help-section code {
  background: #f1f5f9;
  color: #e11d48;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.editor-stats {
  display: flex;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 0.5rem;
  }
  
  .toolbar-btn {
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .editor-footer {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .help-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .markdown-editor {
    background: #1f2937;
    border-color: #374151;
  }
  
  .editor-header,
  .editor-toolbar,
  .editor-footer {
    background: #111827;
    border-color: #374151;
  }
  
  .tab {
    color: #d1d5db;
  }
  
  .tab:hover {
    background: #374151;
    color: #f9fafb;
  }
  
  .markdown-textarea {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .preview-content {
    background: #1f2937;
  }
  
  .preview-h1,
  .preview-h2,
  .preview-h3,
  .preview-p,
  .preview-li {
    color: #f9fafb;
  }
  
  .help-content {
    background: #1f2937;
    border-color: #374151;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .markdown-editor {
    border-width: 2px;
  }
  
  .tab.active {
    background: #000;
    color: #fff;
  }
  
  .toolbar-btn:hover {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .tab,
  .toolbar-btn,
  .fullscreen-btn,
  .preview-link {
    transition: none;
  }
}

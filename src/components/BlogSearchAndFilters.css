/* BlogSearchAndFilters.css - Consolidated search and filters component */

.blog-search-and-filters {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  overflow: hidden;
  border: 1px solid #e9ecef;
  position: sticky;
  top: 0;
}

/* Mobile Toggle Button */
.mobile-filters-toggle {
  display: none;
  width: 100%;
  padding: 1rem;
  background: #f8f9fa;
  border: none;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #495057;
  align-items: center;
  gap: 0.75rem;
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 123, 255, 0.1);
}

.mobile-filters-toggle .toggle-icon {
  font-size: 1.2rem;
}

.mobile-active-indicator {
  margin-left: auto;
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.search-and-filters-content {
  /* Content is always visible on desktop */
}

/* Search Section */
.search-section {
  padding: 1.25rem;
  border-bottom: 1px solid #f1f3f4;
}

.search-form {
  position: relative;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input-container:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  background: white;
}

.search-input {
  flex: 1;
  padding: 0.875rem 1rem;
  border: none;
  outline: none;
  font-size: 1rem;
  background: transparent;
}

.search-input::placeholder {
  color: #6c757d;
}

.clear-search-btn,
.search-submit-btn {
  background: none;
  border: none;
  padding: 0.875rem;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s ease, background-color 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.clear-search-btn:hover,
.search-submit-btn:hover {
  color: #007bff;
  background: rgba(0, 123, 255, 0.1);
}

.search-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Search Suggestions */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item:focus {
  background: #f8f9fa;
  outline: none;
}

.suggestion-icon {
  opacity: 0.6;
}

/* Advanced Toggle Section */
.advanced-toggle-section {
  padding: 0;
  border-bottom: 1px solid #f1f3f4;
}

.advanced-toggle-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: #495057;
  transition: background-color 0.2s ease;
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 123, 255, 0.1);
}

.advanced-toggle-btn:hover {
  background: #e9ecef;
}

.advanced-toggle-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

.toggle-icon {
  font-size: 0.8rem;
  transition: transform 0.2s ease;
}

.active-filters-indicator {
  margin-left: auto;
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Advanced Filters Panel */
.advanced-filters-panel {
  padding: 1.25rem;
  background: #fafbfc;
  border-top: 1px solid #f1f3f4;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-actions {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.clear-all-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-all-btn:hover {
  background: #c82333;
}

/* Search Tips Section */
.search-tips-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-tips-section h4 {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 0.95rem;
  font-weight: 600;
}

.tips-list {
  margin: 0;
  padding-left: 1.25rem;
  list-style-type: disc;
}

.tips-list li {
  margin-bottom: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Filters Grid */
.filters-grid {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.filter-group {
  background: white;
  padding: 1.25rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.filter-group h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tags-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  position: relative;
}

.filter-option:hover {
  background: #f8f9fa;
}

.filter-option input[type="radio"],
.filter-option input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-mark,
.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  margin-right: 0.75rem;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.radio-mark {
  border-radius: 50%;
}

.checkmark {
  border-radius: 3px;
}

.filter-option input[type="radio"]:checked + .radio-mark {
  border-color: #007bff;
  background: #007bff;
  position: relative;
}

.filter-option input[type="radio"]:checked + .radio-mark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
}

.filter-option input[type="checkbox"]:checked + .checkmark {
  border-color: #007bff;
  background: #007bff;
  position: relative;
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.tag-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-name {
  flex: 1;
}

.tag-count {
  color: #6c757d;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.show-more-tags-btn {
  margin-top: 0.75rem;
  background: none;
  border: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #007bff;
  transition: all 0.2s ease;
  width: 100%;
}

.show-more-tags-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

/* Active Filters Summary */
.active-filters-summary {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.active-filters-summary h4 {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 0.95rem;
  font-weight: 600;
}

.active-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.active-filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #007bff;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.remove-filter {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  margin-left: 0.25rem;
  font-size: 1.1rem;
  line-height: 1;
  transition: opacity 0.2s ease;
}

.remove-filter:hover {
  opacity: 0.8;
}

.filters-loading {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-search-and-filters {
    position: static;
    margin-bottom: 1.5rem;
  }

  .mobile-filters-toggle {
    display: flex;
  }

  .search-and-filters-content {
    display: none;
  }

  .search-and-filters-content.mobile-open {
    display: block;
  }

  .search-section {
    padding: 1rem;
  }

  .advanced-filters-panel {
    padding: 1rem;
    max-height: none;
  }

  .filters-grid {
    gap: 1rem;
  }

  .filter-group {
    padding: 1rem;
  }

  .tags-grid {
    grid-template-columns: 1fr;
  }

  .active-filter-tags {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Accessibility and reduced motion */
@media (prefers-reduced-motion: reduce) {
  .search-spinner {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}

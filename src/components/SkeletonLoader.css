/* Skeleton Loading Styles */

/* Base skeleton styles */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  display: inline-block;
}

.skeleton--pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton--wave {
  animation: skeleton-wave 1.5s linear infinite;
}

.skeleton--shimmer {
  animation: skeleton-shimmer 2s linear infinite;
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}

@keyframes skeleton-wave {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Text skeleton */
.skeleton-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skeleton-text__line {
  display: block;
}

/* Avatar skeleton */
.skeleton-avatar {
  flex-shrink: 0;
}

/* Card skeleton */
.skeleton-card {
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff !important;
  margin-bottom: 1rem;
}

.skeleton-card__header {
  margin-bottom: 1rem;
}

.skeleton-card__date {
  margin-top: 0.5rem;
}

.skeleton-card__content {
  margin-top: 1rem;
}

/* Blog list skeleton */
.skeleton-blog-list__header {
  margin-bottom: 2rem;
}

.skeleton-blog-list__title {
  margin-bottom: 0.5rem;
}

.skeleton-blog-list__posts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Navigation skeleton */
.skeleton-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.skeleton-navigation__menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.skeleton-navigation__item {
  display: block;
}

/* Form skeleton */
.skeleton-form {
  max-width: 500px;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.skeleton-form__title {
  margin-bottom: 0.5rem;
}

.skeleton-form__description {
  margin-bottom: 2rem;
}

.skeleton-form__field {
  margin-bottom: 1.5rem;
}

.skeleton-form__label {
  margin-bottom: 0.5rem;
  display: block;
}

.skeleton-form__input {
  display: block;
  margin-bottom: 0.5rem;
}

.skeleton-form__button {
  margin-top: 1rem;
}

/* Table skeleton */
.skeleton-table {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.skeleton-table__header {
  display: flex;
  padding: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  gap: 1rem;
}

.skeleton-table__header-cell {
  flex: 1;
}

.skeleton-table__body {
  display: flex;
  flex-direction: column;
}

.skeleton-table__row {
  display: flex;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  gap: 1rem;
}

.skeleton-table__row:last-child {
  border-bottom: none;
}

.skeleton-table__cell {
  flex: 1;
}

/* Enhanced Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.loading-spinner__circle {
  position: relative;
  display: inline-block;
}

.loading-spinner__path {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--small .loading-spinner__path {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-spinner--large .loading-spinner__path {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.loading-spinner__text {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.loading-spinner--small .loading-spinner__text {
  font-size: 0.75rem;
}

.loading-spinner--large .loading-spinner__text {
  font-size: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Page Loader */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.page-loader__content {
  text-align: center;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Inline Loader */
.inline-loader {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.inline-loader__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.inline-loader--small .inline-loader__spinner {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.inline-loader--medium .inline-loader__spinner {
  width: 20px;
  height: 20px;
  border-width: 2.5px;
}

/* Progress Bar */
.progress-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar__track {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar__fill {
  height: 100%;
  background: linear-gradient(90deg, #0066cc, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-bar__text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  min-width: 40px;
  text-align: right;
}

/* Dark mode disabled to maintain consistent white backgrounds */
/*
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  }

  .skeleton-card,
  .skeleton-form,
  .skeleton-table,
  .skeleton-navigation {
    background: #2d3748;
    border-color: #4a5568;
  }

  .skeleton-table__header {
    background: #1a202c;
  }

  .page-loader {
    background: rgba(45, 55, 72, 0.9);
  }

  .page-loader__content {
    background: #2d3748;
    color: #e2e8f0;
  }

  .loading-spinner__text {
    color: #cbd5e0;
  }

  .progress-bar__track {
    background: #4a5568;
  }

  .progress-bar__text {
    color: #e2e8f0;
  }
}
*/

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .skeleton--pulse,
  .skeleton--wave,
  .skeleton--shimmer {
    animation: none;
  }

  .loading-spinner__path {
    animation: none;
    border-top-color: transparent;
    border-right-color: #0066cc;
  }

  .inline-loader__spinner {
    animation: none;
    border-top-color: transparent;
    border-right-color: currentColor;
  }

  .progress-bar__fill {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .skeleton {
    background: #ccc;
  }

  .loading-spinner__path {
    border-color: #000;
    border-top-color: #666;
  }

  .progress-bar__fill {
    background: #000;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .skeleton-navigation {
    padding: 1rem;
  }

  .skeleton-navigation__menu {
    gap: 1rem;
  }

  .skeleton-form {
    padding: 1.5rem;
  }

  .skeleton-card {
    padding: 1rem;
  }

  .page-loader__content {
    margin: 1rem;
    padding: 1.5rem;
  }
}

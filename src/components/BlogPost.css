/* Enhanced BlogPost.css with markdown support */

.blog-content {
    max-width: 900px;
    margin: 40px auto;
    padding: 2rem;
    background: white !important;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    line-height: 1.7;
    color: #374151 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.blog-post-header {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e5e7eb;
}

.back-button {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.back-button:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
    transform: translateX(-2px);
}

.back-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.blog-post-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    color: #111827;
    line-height: 1.2;
}

.blog-post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.blog-post-date {
    font-weight: 600;
    color: #374151;
}

.blog-post-author {
    color: #6b7280;
}

.blog-post-read-time {
    color: #9ca3af;
    font-style: italic;
}

.blog-post-taxonomy {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.blog-post-categories,
.blog-post-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
}

.taxonomy-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.category-tag {
    background: #3b82f6;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tag-item {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    border: 1px solid #d1d5db;
}

.blog-post-content {
    margin-bottom: 3rem;
}

/* Content Styles for Markdown */
.content-h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 2rem 0 1rem 0;
    color: #111827;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.content-h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 1.75rem 0 1rem 0;
    color: #1f2937;
}

.content-h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
    color: #374151;
}

.content-p {
    font-size: 1.125rem;
    margin: 1rem 0;
    line-height: 1.8;
    color: #374151;
}

.content-quote {
    border-left: 4px solid #3b82f6;
    padding: 1rem 0 1rem 1.5rem;
    margin: 1.5rem 0;
    background: #f8fafc;
    font-style: italic;
    color: #64748b;
    border-radius: 0 6px 6px 0;
}

.content-code-inline {
    background: #f1f5f9;
    color: #e11d48;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875em;
    font-weight: 500;
}

.content-pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.content-code-block {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

.content-ul,
.content-ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.content-li {
    margin: 0.5rem 0;
    line-height: 1.7;
    color: #374151;
}

.content-link {
    color: #3b82f6;
    text-decoration: underline;
    text-decoration-color: transparent;
    transition: all 0.2s ease;
    font-weight: 500;
}

.content-link:hover {
    text-decoration-color: #3b82f6;
    color: #1d4ed8;
}

.content-img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 2rem 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.blog-post-footer {
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
    text-align: center;
}

.no-content {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 3rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
}

/* Loading and Error States */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.error-container,
.not-found-container {
    text-align: center;
    padding: 3rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    color: #991b1b;
}

.error-container h2,
.not-found-container h2 {
    color: #991b1b;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-content {
        margin: 20px;
        padding: 1.5rem;
    }

    .blog-post-title {
        font-size: 2rem;
    }

    .blog-post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .blog-post-categories,
    .blog-post-tags {
        flex-direction: column;
        align-items: flex-start;
    }

    .content-h1 {
        font-size: 1.75rem;
    }

    .content-h2 {
        font-size: 1.375rem;
    }

    .content-h3 {
        font-size: 1.125rem;
    }

    .content-p {
        font-size: 1rem;
    }

    .content-pre {
        padding: 1rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .blog-content {
        margin: 10px;
        padding: 1rem;
    }

    .blog-post-title {
        font-size: 1.75rem;
    }

    .back-button {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .category-tag,
    .tag-item {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }
}

/* Dark mode disabled to maintain consistent white backgrounds */
/*
@media (prefers-color-scheme: dark) {
    .blog-content {
        background: #1f2937;
        color: #f9fafb;
    }

    .blog-post-header {
        border-bottom-color: #374151;
    }

    .blog-post-title {
        color: #f9fafb;
    }

    .content-h1,
    .content-h2,
    .content-h3 {
        color: #f9fafb;
        border-bottom-color: #374151;
    }

    .content-p,
    .content-li {
        color: #e5e7eb;
    }

    .content-quote {
        background: #374151;
        color: #d1d5db;
        border-left-color: #60a5fa;
    }

    .content-code-inline {
        background: #374151;
        color: #fbbf24;
    }

    .tag-item {
        background: #374151;
        color: #e5e7eb;
        border-color: #4b5563;
    }

    .back-button {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }

    .back-button:hover {
        background: #4b5563;
    }

    .blog-post-footer {
        border-top-color: #374151;
    }
}
*/

/* High contrast mode */
@media (prefers-contrast: high) {
    .blog-content {
        border: 2px solid #000;
    }

    .content-link {
        text-decoration: underline;
        text-decoration-color: currentColor;
    }

    .category-tag,
    .tag-item {
        border: 1px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .back-button,
    .content-link {
        transition: none;
    }

    .back-button:hover {
        transform: none;
    }
}
.contact-container {
    max-width: 500px;
    margin: 50px auto;
    margin-top: 200px;
    padding: 20px;
    text-align: center;
    background: #fff;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, .5);
    border-radius: 10px;
}

h2 {
    color: #333;
}

p {
    color: #666;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.input-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #444;
}

.input-group input,
.input-group textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
}

.input-group textarea {
    height: 100px;
    resize: none;
}

.submit-btn {
    padding: 12px;
    border: none;
    border-radius: 5px;
    background-color: #035aa6;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: 0.3s;
}

.submit-btn:hover {
    color: #0596F2;
}

/* Enhanced Form Validation Styles */

.contact-header {
  text-align: center;
  margin-bottom: 2rem;
}

.contact-header h2 {
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 600;
}

.contact-header p {
  margin-bottom: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.form-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.form-status--success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-status--error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.form-status__icon {
  flex-shrink: 0;
}

.form-status__message {
  line-height: 1.4;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field--error .form-input,
.form-field--error .form-textarea {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-field--valid .form-input,
.form-field--valid .form-textarea {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-label__required {
  color: #dc3545;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: #dc3545;
  font-size: 0.875rem;
  font-weight: 500;
}

.form-help {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.submit-btn:disabled:hover {
  color: white;
}

.submit-btn .inline-loader {
  margin-right: 0.5rem;
}
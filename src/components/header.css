/* Header container */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: #FFFFFF;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
}

/* Header line (added as a pseudo-element) */
.navbar::after {
  content: '';
  display: block;
  width: 20%;
  /* Adjust width as needed */
  height: 1px;
  /* Thickness of the line */
  background-color: black;
  /* Line color */
  margin: 0 auto;
  /* Center the line */
  position: absolute;
  bottom: .0;
  /* Position at the bottom of the header */
  left: 0;
  right: 0;
}

.nav-link {
  color: black !important;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #035aa6 !important;
}

/* Logo styles */
.logo-image {
  width: 150px;
  height: auto;
}

/* Hamburger menu styles */
.hamburger {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
}

.bar {
  width: 25px;
  height: 2px;
  background-color: black;
  margin: 4px 0;
}

/* Navigation styles */
.nav {
  display: none;
  flex-direction: column;
  align-items: flex-start;
}

.nav.open {
  display: flex;
  position: absolute;
  top: 80px;
  right: 20px;
  background-color: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0px 0px 3px rgb(0, 0, 0);
}

.nav-list {
  display: flex;
  flex-direction: column;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-list .nav-item {
  margin-left: 20px;
}

.nav.open .nav-item {
  margin: 10px 0;
  display: flex;
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #fff;
    z-index: 1000;
  }

  .logo-image {
    width: 120px;
  }

  .nav {
    display: none;
    flex-direction: column;
    align-items: flex-start;
    width: auto;
    max-width: 90%;
    position: absolute;
    top: 70px;
    right: 10px;
    background-color: #fff;
    z-index: 999;
    padding: 10px 20px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    overflow-y: auto;
    max-height: 60vh;
    /* Prevents it from blocking entire screen */
  }

  .nav.open {
    display: flex;
  }

  .nav-list {
    width: 100%;
  }

  .nav-list .nav-item {
    margin: 10px 0;
    width: 100%;
  }

  .nav-link {
    font-size: 18px;
    padding: 10px 0;
    width: 100%;
    display: block;
  }

  .hamburger {
    display: flex;
  }
}
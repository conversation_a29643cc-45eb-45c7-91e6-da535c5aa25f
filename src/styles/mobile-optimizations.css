/* Mobile Optimizations - Enhanced mobile experience */

/* Global mobile improvements */
* {
  /* Prevent text size adjustment on orientation change */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Remove tap highlight on all elements by default */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Improve touch scrolling on iOS */
body {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Ensure all interactive elements have proper touch targets */
button,
[role="button"],
input[type="submit"],
input[type="button"],
input[type="reset"],
.clickable {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 123, 255, 0.1);
}

/* Improve form inputs on mobile */
input,
textarea,
select {
  /* Prevent zoom on iOS when focusing inputs */
  font-size: 16px;
  touch-action: manipulation;
}

@media (max-width: 768px) {
  input,
  textarea,
  select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.75rem;
    border-radius: 8px;
  }
}

/* Mobile-specific link improvements */
a {
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 123, 255, 0.1);
}

/* Improve mobile navigation */
@media (max-width: 768px) {
  /* Ensure navigation is touch-friendly */
  nav a,
  .nav-link {
    padding: 0.75rem 1rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }
}

/* Mobile-specific spacing improvements */
@media (max-width: 768px) {
  /* Increase spacing for better touch targets */
  .blog-post-preview {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
  }
  
  /* Improve button spacing */
  .button,
  button {
    margin: 0.5rem 0;
    padding: 0.75rem 1.5rem;
  }
  
  /* Better spacing for form elements */
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  /* Improve card spacing */
  .card {
    margin: 1rem 0.5rem;
    padding: 1.5rem;
  }
}

/* Mobile-specific typography improvements */
@media (max-width: 768px) {
  /* Ensure readable text sizes */
  body {
    font-size: 16px;
    line-height: 1.6;
  }
  
  h1 {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.75rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
  }
  
  /* Improve paragraph spacing */
  p {
    margin-bottom: 1.25rem;
  }
}

/* Mobile-specific performance optimizations */
@media (max-width: 768px) {
  /* Reduce animations on mobile for better performance */
  * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }
  
  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
  }
}

/* Improve mobile accessibility */
@media (max-width: 768px) {
  /* Ensure focus indicators are visible */
  *:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
  
  /* Improve skip links for mobile screen readers */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #007bff;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
  }
  
  .skip-link:focus {
    top: 6px;
  }
}

/* Mobile-specific loading states */
@media (max-width: 768px) {
  .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 2px;
  }
  
  .loading-container {
    padding: 2rem 1rem;
  }
}

/* Improve mobile search experience */
@media (max-width: 768px) {
  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.875rem 1rem;
  }
  
  .search-suggestions {
    max-height: 50vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .suggestion-item {
    padding: 1rem;
    min-height: 44px;
  }
}

/* Mobile-specific modal and overlay improvements */
@media (max-width: 768px) {
  .modal,
  .overlay {
    padding: 1rem;
  }
  
  .modal-content {
    margin: 0;
    max-height: 90vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Improve mobile table experience */
@media (max-width: 768px) {
  table {
    font-size: 14px;
  }
  
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Mobile-specific error and success states */
@media (max-width: 768px) {
  .error-message,
  .success-message,
  .warning-message {
    padding: 1rem;
    margin: 1rem 0.5rem;
    border-radius: 8px;
  }
}

/* Improve mobile footer */
@media (max-width: 768px) {
  footer {
    padding: 2rem 1rem;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-link {
    padding: 0.5rem 0;
    min-height: 44px;
    display: flex;
    align-items: center;
  }
}

/* Mobile-specific utility classes */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-only {
    display: block !important;
  }
  
  .mobile-center {
    text-align: center !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
}

/* Improve mobile performance with will-change */
@media (max-width: 768px) {
  .scroll-container {
    will-change: scroll-position;
  }
  
  .animated-element {
    will-change: transform;
  }
}

/* Mobile-specific print styles */
@media print {
  .mobile-filters-toggle,
  .search-section,
  .advanced-filters-panel {
    display: none !important;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .blog-header {
    padding: 1rem 0;
  }
  
  .blog-title {
    font-size: 1.75rem;
  }
  
  .mobile-filters-toggle {
    padding: 0.75rem 1rem;
  }
}

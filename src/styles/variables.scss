// SCSS Variables for consistent theming and performance optimization

// Brand Colors
$primary: #0066CC;
$secondary: #0596F2;
$accent: #035aa6;

// Semantic Colors
$success: #28a745;
$info: #17a2b8;
$warning: #ffc107;
$danger: #dc3545;
$light: #f8f9fa;
$dark: #343a40;

// Grayscale
$white: #ffffff;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$black: #000000;

// Typography
$font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
  'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
$font-family-monospace: 'SFMono-Regular', <PERSON>sol<PERSON>, 'Liberation Mono', Menlo, Courier, monospace;

$font-size-base: 1rem;
$font-size-sm: 0.875rem;
$font-size-lg: 1.25rem;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$line-height-base: 1.6;
$line-height-sm: 1.4;
$line-height-lg: 1.8;

// Spacing
$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
);

// Borders
$border-width: 1px;
$border-color: $gray-300;
$border-radius: 0.375rem;
$border-radius-sm: 0.25rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 1rem;

// Shadows
$box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// Transitions
$transition-base: all 0.2s ease-in-out;
$transition-fade: opacity 0.15s linear;
$transition-collapse: height 0.35s ease;

// Breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// Container max widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

// Z-index layers
$zindex-dropdown: 1000;
$zindex-sticky: 1020;
$zindex-fixed: 1030;
$zindex-modal-backdrop: 1040;
$zindex-modal: 1050;
$zindex-popover: 1060;
$zindex-tooltip: 1070;

// Performance optimizations
$enable-caret: true;
$enable-rounded: true;
$enable-shadows: true;
$enable-gradients: true;
$enable-transitions: true;
$enable-reduced-motion: true;
$enable-grid-classes: true;
$enable-button-pointers: true;

// Component specific variables
$navbar-height: 80px;
$footer-height: 200px;

// Animation durations
$animation-duration-fast: 0.15s;
$animation-duration-base: 0.3s;
$animation-duration-slow: 0.5s;

// Mixins for performance
@mixin transition($property: all, $duration: $animation-duration-base, $timing: ease-in-out) {
  transition: $property $duration $timing;
  
  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
}

@mixin hover-lift {
  @include transition(transform);
  
  &:hover {
    transform: translateY(-2px);
  }
}

@mixin text-gradient($start: $primary, $end: $secondary) {
  background: linear-gradient(90deg, $start, $end);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@mixin glass-effect($opacity: 0.85) {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, $opacity);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

// Responsive mixins
@mixin media-breakpoint-up($name) {
  $min: map-get($grid-breakpoints, $name);
  @if $min != 0 {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name) {
  $max: map-get($grid-breakpoints, $name) - 0.02;
  @media (max-width: $max) {
    @content;
  }
}

/* Critical CSS - Above the fold styles */
/* This CSS is inlined in the HTML head for faster initial rendering */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

/* Header styles - critical as it's above the fold */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background-color: #FFFFFF;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo-image {
  width: 150px;
  height: auto;
}

/* Main content container */
.main-content {
  margin-top: 80px; /* Account for fixed header */
  min-height: 100vh;
}

/* First card - critical as it's above the fold */
.card {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.image {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  border-radius: 12px;
}

.text-content {
  flex: 2;
  min-width: 300px;
}

.text-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #0066CC;
  font-weight: 700;
}

.text-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

/* Button styles - critical for CTA */
.button {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, #0066CC, #0596F2);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: none;
  cursor: pointer;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(5, 150, 242, 0.3);
  color: white;
  text-decoration: none;
}

/* Loading states - critical for perceived performance */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  flex-direction: column;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design - critical for mobile users */
@media (max-width: 768px) {
  .navbar {
    padding: 10px 15px;
  }
  
  .logo-image {
    width: 120px;
  }
  
  .card {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .content {
    flex-direction: column;
    text-align: center;
  }
  
  .text-content h2 {
    font-size: 2rem;
  }
  
  .text-content p {
    font-size: 1rem;
  }
}

/* Accessibility - critical for all users */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.button:focus,
.nav-link:focus {
  outline: 2px solid #0066CC;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }
  
  .button {
    border: 2px solid #000;
  }
}

/* Accessibility Styles */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  z-index: 1000;
  font-weight: bold;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 0;
}

/* Focus management */
.focus-visible *:focus {
  outline: 2px solid #0066CC;
  outline-offset: 2px;
  border-radius: 2px;
}

.focus-visible button:focus,
.focus-visible a:focus,
.focus-visible input:focus,
.focus-visible textarea:focus,
.focus-visible select:focus {
  outline: 2px solid #0066CC;
  outline-offset: 2px;
}

/* Remove focus outline when not using keyboard */
:not(.focus-visible) *:focus {
  outline: none;
}

/* High contrast mode support */
.high-contrast {
  --primary-color: #000;
  --secondary-color: #fff;
  --accent-color: #0066CC;
  --text-color: #000;
  --background-color: #fff;
  --border-color: #000;
}

.high-contrast .card {
  border: 2px solid var(--border-color);
  background: var(--background-color);
  color: var(--text-color);
}

.high-contrast .button {
  border: 2px solid var(--border-color);
  background: var(--background-color);
  color: var(--text-color);
}

.high-contrast .button:hover,
.high-contrast .button:focus {
  background: var(--text-color);
  color: var(--background-color);
}

/* Reduced motion preferences */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Enhanced focus indicators */
.enhanced-focus *:focus {
  outline: 3px solid #0066CC;
  outline-offset: 3px;
  box-shadow: 0 0 0 1px #fff, 0 0 0 4px #0066CC;
}

/* Keyboard navigation helpers */
.keyboard-nav {
  position: relative;
}

.keyboard-nav::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: 4px;
  pointer-events: none;
  transition: border-color 0.2s ease;
}

.keyboard-nav:focus::after {
  border-color: #0066CC;
}

/* ARIA live regions */
[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Form accessibility */
.form-group {
  position: relative;
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  border-color: #0066CC;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  outline: none;
}

.form-input:invalid {
  border-color: #dc3545;
}

.form-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.form-help {
  color: #666;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Button accessibility */
.btn-accessible {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* Minimum touch target size */
  min-width: 44px;
  padding: 0.75rem 1.5rem;
  border: 2px solid transparent;
  border-radius: 4px;
  background: #0066CC;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-accessible:hover {
  background: #0052a3;
  transform: translateY(-1px);
}

.btn-accessible:focus {
  outline: 2px solid #0066CC;
  outline-offset: 2px;
}

.btn-accessible:disabled {
  background: #ccc;
  color: #666;
  cursor: not-allowed;
  transform: none;
}

.btn-accessible[aria-pressed="true"] {
  background: #0052a3;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Loading states */
.loading-accessible {
  position: relative;
}

.loading-accessible::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.error-accessible {
  border: 2px solid #dc3545;
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.error-accessible::before {
  content: '⚠️ ';
  font-weight: bold;
}

/* Success states */
.success-accessible {
  border: 2px solid #28a745;
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.success-accessible::before {
  content: '✅ ';
  font-weight: bold;
}

/* Navigation accessibility */
.nav-accessible {
  position: relative;
}

.nav-accessible ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-accessible li {
  position: relative;
}

.nav-accessible a {
  display: block;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.nav-accessible a:hover,
.nav-accessible a:focus {
  background-color: #f8f9fa;
  outline: 2px solid #0066CC;
  outline-offset: -2px;
}

.nav-accessible a[aria-current="page"] {
  background-color: #0066CC;
  color: #fff;
  font-weight: 600;
}

/* Media queries for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  .card,
  .button,
  .form-input {
    border-width: 2px;
    border-style: solid;
  }
}

@media (max-width: 768px) {
  .btn-accessible {
    min-height: 48px; /* Larger touch targets on mobile */
    min-width: 48px;
  }
}
